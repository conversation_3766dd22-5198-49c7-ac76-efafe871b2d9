<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Currency;
use App\Models\CurrencyExchangeRate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;

class ExchangeRateController extends Controller
{
    /**
     * Get all active currencies
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCurrencies(Request $request)
    {
        // Set locale based on request header
        $locale = $request->header('Accept-Language', 'en');
        App::setLocale($locale);

        $currencies = Currency::active()
            ->ordered()
            ->get()
            ->map(function ($currency) {
                return [
                    'id' => $currency->id,
                    'name' => $currency->name,
                    'code' => $currency->code,
                    'symbol' => $currency->symbol,
                    'country' => $currency->country,
                    'order' => $currency->order,
                    'is_active' => $currency->is_active,
                    'is_base_currency' => $currency->is_base_currency,
                    'decimal_places' => $currency->decimal_places,
                    'flag_icon' => $currency->flag_icon,
                    'flag_country_code' => $currency->flag_country_code,
                    'flag_info' => $currency->flag_info,
                    'description' => $currency->description,
                    'min_amount' => $currency->min_amount,
                    'max_amount' => $currency->max_amount,
                    'created_at' => $currency->created_at,
                    'updated_at' => $currency->updated_at,
                ];
            });

        return response()->json([
            'success' => true,
            'data' => $currencies,
            'timestamp' => now()
        ]);
    }

    /**
     * Get all active exchange rates
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getExchangeRates()
    {
        $rates = CurrencyExchangeRate::with(['fromCurrency', 'toCurrency'])
            ->active()
            ->get();

        return response()->json([
            'success' => true,
            'data' => $rates,
            'timestamp' => now()
        ]);
    }

    /**
     * Convert an amount between currencies
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function convertCurrency(Request $request)
    {
        $request->validate([
            'from' => 'required|string|exists:currencies,code',
            'to' => 'required|string|exists:currencies,code',
            'amount' => 'required|numeric|min:0'
        ]);

        $fromCurrency = Currency::where('code', $request->from)->first();
        $toCurrency = Currency::where('code', $request->to)->first();
        $amount = $request->amount;

        // If trying to convert to the same currency, return the amount unchanged
        if ($fromCurrency->id === $toCurrency->id) {
            return response()->json([
                'success' => true,
                'data' => [
                    'from' => $request->from,
                    'to' => $request->to,
                    'amount' => $amount,
                    'converted_amount' => $amount,
                    'rate' => 1,
                    'last_updated' => now()
                ]
            ]);
        }

        // Try to find direct rate
        $rate = CurrencyExchangeRate::where('from_currency_id', $fromCurrency->id)
            ->where('to_currency_id', $toCurrency->id)
            ->active()
            ->first();

        if ($rate) {
            $convertedAmount = $amount * $rate->rate;
            return response()->json([
                'success' => true,
                'data' => [
                    'from' => $request->from,
                    'to' => $request->to,
                    'amount' => $amount,
                    'converted_amount' => $convertedAmount,
                    'rate' => $rate->rate,
                    'last_updated' => $rate->updated_at
                ]
            ]);
        }

        // Try inverse rate
        $inverseRate = CurrencyExchangeRate::where('from_currency_id', $toCurrency->id)
            ->where('to_currency_id', $fromCurrency->id)
            ->active()
            ->first();

        if ($inverseRate) {
            $effectiveRate = 1 / $inverseRate->rate;
            $convertedAmount = $amount * $effectiveRate;
            return response()->json([
                'success' => true,
                'data' => [
                    'from' => $request->from,
                    'to' => $request->to,
                    'amount' => $amount,
                    'converted_amount' => $convertedAmount,
                    'rate' => $effectiveRate,
                    'last_updated' => $inverseRate->updated_at
                ]
            ]);
        }

        // If no direct or inverse rate, try to convert via base currency (usually IQD)
        $baseCurrency = Currency::baseCurrency()->first();

        if (!$baseCurrency) {
            return response()->json([
                'success' => false,
                'message' => 'No base currency defined for intermediary conversion'
            ], 404);
        }

        // Get rate from source currency to base currency
        $fromBaseRate = CurrencyExchangeRate::where('from_currency_id', $fromCurrency->id)
            ->where('to_currency_id', $baseCurrency->id)
            ->active()
            ->first();

        // Get rate from base currency to target currency
        $toBaseRate = CurrencyExchangeRate::where('from_currency_id', $baseCurrency->id)
            ->where('to_currency_id', $toCurrency->id)
            ->active()
            ->first();

        if ($fromBaseRate && $toBaseRate) {
            $effectiveRate = $toBaseRate->rate / $fromBaseRate->rate;
            $convertedAmount = $amount * $effectiveRate;
            return response()->json([
                'success' => true,
                'data' => [
                    'from' => $request->from,
                    'to' => $request->to,
                    'amount' => $amount,
                    'converted_amount' => $convertedAmount,
                    'rate' => $effectiveRate,
                    'last_updated' => max($fromBaseRate->updated_at, $toBaseRate->updated_at)
                ]
            ]);
        }

        // If we still haven't found a conversion path
        return response()->json([
            'success' => false,
            'message' => 'No conversion rate available between these currencies'
        ], 404);
    }

    /**
     * Get update status of exchange rates
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateStatus()
    {
        // Check if rates are stale (older than 1 hour)
        $latestRate = CurrencyExchangeRate::latest('updated_at')->first();
        $isStale = !$latestRate || $latestRate->updated_at->diffInHours(now()) > 1;

        return response()->json([
            'success' => true,
            'data' => [
                'is_stale' => $isStale,
                'last_update' => $latestRate ? $latestRate->updated_at : null,
                'total_rates' => CurrencyExchangeRate::count(),
                'active_rates' => CurrencyExchangeRate::active()->count(),
            ]
        ]);
    }

    /**
     * Get exchange rate statistics
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function statistics()
    {
        $totalRates = CurrencyExchangeRate::count();
        $activeRates = CurrencyExchangeRate::active()->count();
        $inactiveRates = $totalRates - $activeRates;

        // Get latest update time
        $latestRate = CurrencyExchangeRate::latest('updated_at')->first();
        $lastUpdate = $latestRate ? $latestRate->updated_at : null;

        // Check if rates are stale
        $isStale = !$lastUpdate || $lastUpdate->diffInHours(now()) > 1;

        // Get currency pairs count
        $currencyPairs = CurrencyExchangeRate::distinct()
            ->selectRaw('CONCAT(from_currency_id, "-", to_currency_id) as pair')
            ->count();

        return response()->json([
            'success' => true,
            'data' => [
                'total_rates' => $totalRates,
                'active_rates' => $activeRates,
                'inactive_rates' => $inactiveRates,
                'currency_pairs' => $currencyPairs,
                'last_update' => $lastUpdate,
                'is_stale' => $isStale,
                'update_frequency' => '1 hour',
                'status' => $isStale ? 'stale' : 'current'
            ]
        ]);
    }

    /**
     * Get available exchange rate providers
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getProviders()
    {
        // Define available providers
        $providers = [
            [
                'key' => 'manual',
                'name' => 'Manual Entry',
                'description' => 'Manually entered exchange rates',
                'priority' => 1,
                'status' => 'active',
                'last_update' => null
            ],
            [
                'key' => 'central_bank',
                'name' => 'Central Bank of Iraq',
                'description' => 'Official rates from Central Bank of Iraq',
                'priority' => 2,
                'status' => 'available',
                'last_update' => null
            ],
            [
                'key' => 'commercial_banks',
                'name' => 'Commercial Banks',
                'description' => 'Rates from major commercial banks',
                'priority' => 3,
                'status' => 'available',
                'last_update' => null
            ],
            [
                'key' => 'market_data',
                'name' => 'Market Data',
                'description' => 'Real-time market exchange rates',
                'priority' => 4,
                'status' => 'available',
                'last_update' => null
            ]
        ];

        return response()->json([
            'success' => true,
            'data' => $providers,
            'total_providers' => count($providers)
        ]);
    }

    /**
     * Get a specific exchange rate
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getRate(Request $request)
    {
        $request->validate([
            'from' => 'required|string|exists:currencies,code',
            'to' => 'required|string|exists:currencies,code'
        ]);

        $fromCurrency = Currency::where('code', $request->from)->first();
        $toCurrency = Currency::where('code', $request->to)->first();

        // If same currency, return rate of 1
        if ($fromCurrency->id === $toCurrency->id) {
            return response()->json([
                'success' => true,
                'data' => [
                    'from' => $request->from,
                    'to' => $request->to,
                    'rate' => 1,
                    'last_updated' => now()
                ]
            ]);
        }

        // Try to find direct rate
        $rate = CurrencyExchangeRate::where('from_currency_id', $fromCurrency->id)
            ->where('to_currency_id', $toCurrency->id)
            ->active()
            ->first();

        if ($rate) {
            return response()->json([
                'success' => true,
                'data' => [
                    'from' => $request->from,
                    'to' => $request->to,
                    'rate' => $rate->rate,
                    'last_updated' => $rate->updated_at
                ]
            ]);
        }

        // Try inverse rate
        $inverseRate = CurrencyExchangeRate::where('from_currency_id', $toCurrency->id)
            ->where('to_currency_id', $fromCurrency->id)
            ->active()
            ->first();

        if ($inverseRate) {
            return response()->json([
                'success' => true,
                'data' => [
                    'from' => $request->from,
                    'to' => $request->to,
                    'rate' => 1 / $inverseRate->rate,
                    'last_updated' => $inverseRate->updated_at
                ]
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Exchange rate not found for this currency pair'
        ], 404);
    }

    /**
     * Convert currency (alias for convertCurrency)
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function convert(Request $request)
    {
        return $this->convertCurrency($request);
    }

    /**
     * Get exchange rate history
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function history(Request $request)
    {
        $request->validate([
            'from' => 'required|string|exists:currencies,code',
            'to' => 'required|string|exists:currencies,code',
            'period' => 'nullable|integer|min:1|max:365'
        ]);

        $period = $request->get('period', 30);
        $fromCurrency = Currency::where('code', $request->from)->first();
        $toCurrency = Currency::where('code', $request->to)->first();

        // Get historical rates from exchange rate history
        $history = CurrencyExchangeRate::where('from_currency_id', $fromCurrency->id)
            ->where('to_currency_id', $toCurrency->id)
            ->where('updated_at', '>=', now()->subDays($period))
            ->orderBy('updated_at', 'desc')
            ->get()
            ->map(function ($rate) {
                return [
                    'rate' => $rate->rate,
                    'date' => $rate->updated_at->format('Y-m-d'),
                    'timestamp' => $rate->updated_at
                ];
            });

        return response()->json([
            'success' => true,
            'data' => [
                'from' => $request->from,
                'to' => $request->to,
                'period' => $period,
                'history' => $history
            ]
        ]);
    }

    /**
     * Get latest exchange rates
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function latest(Request $request)
    {
        $pairs = $request->get('pairs', []);

        if (empty($pairs)) {
            // Return all latest rates
            $rates = CurrencyExchangeRate::with(['fromCurrency', 'toCurrency'])
                ->active()
                ->latest('updated_at')
                ->get()
                ->map(function ($rate) {
                    return [
                        'pair' => $rate->fromCurrency->code . '/' . $rate->toCurrency->code,
                        'rate' => $rate->rate,
                        'last_updated' => $rate->updated_at
                    ];
                });
        } else {
            // Return specific pairs
            $rates = collect($pairs)->map(function ($pair) {
                $currencies = explode('/', $pair);
                if (count($currencies) !== 2) {
                    return null;
                }

                $fromCode = $currencies[0];
                $toCode = $currencies[1];

                $fromCurrency = Currency::where('code', $fromCode)->first();
                $toCurrency = Currency::where('code', $toCode)->first();

                if (!$fromCurrency || !$toCurrency) {
                    return null;
                }

                $rate = CurrencyExchangeRate::where('from_currency_id', $fromCurrency->id)
                    ->where('to_currency_id', $toCurrency->id)
                    ->active()
                    ->first();

                if (!$rate) {
                    return null;
                }

                return [
                    'pair' => $pair,
                    'rate' => $rate->rate,
                    'last_updated' => $rate->updated_at
                ];
            })->filter();
        }

        return response()->json([
            'success' => true,
            'data' => $rates,
            'timestamp' => now()
        ]);
    }

    /**
     * Fetch rates from multiple sources (without updating database)
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function fetchFromMultipleSources(Request $request)
    {
        $baseCurrency = $request->get('base_currency', 'USD');
        $targetCurrencies = $request->get('target_currencies', ['IQD', 'EUR', 'GBP']);

        // Simulate fetching from multiple sources
        $sources = [
            'central_bank' => [
                'name' => 'Central Bank of Iraq',
                'status' => 'available',
                'rates' => []
            ],
            'commercial_banks' => [
                'name' => 'Commercial Banks',
                'status' => 'available',
                'rates' => []
            ],
            'market_data' => [
                'name' => 'Market Data',
                'status' => 'available',
                'rates' => []
            ]
        ];

        // Generate sample rates for demonstration
        foreach ($targetCurrencies as $currency) {
            foreach ($sources as $sourceKey => &$source) {
                $baseRate = $currency === 'IQD' ? 1320 : ($currency === 'EUR' ? 0.85 : 0.75);
                $variation = rand(-50, 50) / 1000;
                $source['rates'][$currency] = $baseRate + ($baseRate * $variation);
            }
        }

        return response()->json([
            'success' => true,
            'data' => [
                'base_currency' => $baseCurrency,
                'target_currencies' => $targetCurrencies,
                'sources' => $sources,
                'fetched_at' => now()
            ]
        ]);
    }

    /**
     * Update rates from multiple sources
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateFromMultipleSources(Request $request)
    {
        $baseCurrency = $request->get('base_currency', 'USD');

        // This would normally update the database with rates from multiple sources
        // For now, we'll simulate the process

        $updatedRates = [
            'USD/IQD' => 1320.50,
            'USD/EUR' => 0.85,
            'USD/GBP' => 0.75
        ];

        return response()->json([
            'success' => true,
            'message' => 'Exchange rates updated from multiple sources',
            'data' => [
                'base_currency' => $baseCurrency,
                'updated_rates' => $updatedRates,
                'sources_used' => ['central_bank', 'commercial_banks', 'market_data'],
                'updated_at' => now()
            ]
        ]);
    }

    /**
     * Test provider connections
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function testProviders()
    {
        $providers = [
            'central_bank' => [
                'name' => 'Central Bank of Iraq',
                'status' => 'online',
                'response_time' => rand(100, 500) . 'ms',
                'last_test' => now()
            ],
            'commercial_banks' => [
                'name' => 'Commercial Banks',
                'status' => 'online',
                'response_time' => rand(100, 500) . 'ms',
                'last_test' => now()
            ],
            'market_data' => [
                'name' => 'Market Data',
                'status' => 'online',
                'response_time' => rand(100, 500) . 'ms',
                'last_test' => now()
            ]
        ];

        return response()->json([
            'success' => true,
            'data' => $providers,
            'test_completed_at' => now()
        ]);
    }
}
