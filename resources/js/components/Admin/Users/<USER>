<template>
  <q-dialog v-model="showDialog" persistent>
    <q-card class="create-user-dialog">
      <q-card-section class="dialog-header">
        <div class="text-h6">Create New User</div>
        <q-btn flat round dense icon="close" @click="closeDialog" />
      </q-card-section>

      <q-separator />

      <q-card-section class="dialog-content">
        <q-form @submit="createUser" class="q-gutter-md">
          <!-- Avatar Upload -->
          <div class="avatar-section">
            <q-avatar size="80px" class="avatar-preview">
              <img v-if="form.avatar_preview" :src="form.avatar_preview" />
              <img v-else src="/images/default-avatar.svg" />
            </q-avatar>
            <q-btn
              flat
              icon="camera_alt"
              label="Upload Avatar"
              @click="$refs.avatarInput.click()"
              class="q-ml-md"
            />
            <input
              ref="avatarInput"
              type="file"
              accept="image/*"
              @change="handleAvatarUpload"
              style="display: none"
            />
          </div>

          <!-- Basic Information -->
          <div class="row q-gutter-md">
            <div class="col-12 col-sm-6">
              <q-input
                v-model="form.name"
                label="Full Name *"
                outlined
                :rules="[val => !!val || 'Name is required']"
              />
            </div>
            <div class="col-12 col-sm-6">
              <q-input
                v-model="form.email"
                label="Email Address *"
                type="email"
                outlined
                :rules="[
                  val => !!val || 'Email is required',
                  val => /.+@.+\..+/.test(val) || 'Email must be valid'
                ]"
              />
            </div>
          </div>

          <!-- Password -->
          <div class="row q-gutter-md">
            <div class="col-12 col-sm-6">
              <q-input
                v-model="form.password"
                label="Password *"
                :type="showPassword ? 'text' : 'password'"
                outlined
                :rules="[
                  val => !!val || 'Password is required',
                  val => val.length >= 8 || 'Password must be at least 8 characters'
                ]"
              >
                <template v-slot:append>
                  <q-icon
                    :name="showPassword ? 'visibility_off' : 'visibility'"
                    class="cursor-pointer"
                    @click="showPassword = !showPassword"
                  />
                </template>
              </q-input>
            </div>
            <div class="col-12 col-sm-6">
              <q-input
                v-model="form.password_confirmation"
                label="Confirm Password *"
                :type="showPasswordConfirm ? 'text' : 'password'"
                outlined
                :rules="[
                  val => !!val || 'Password confirmation is required',
                  val => val === form.password || 'Passwords do not match'
                ]"
              >
                <template v-slot:append>
                  <q-icon
                    :name="showPasswordConfirm ? 'visibility_off' : 'visibility'"
                    class="cursor-pointer"
                    @click="showPasswordConfirm = !showPasswordConfirm"
                  />
                </template>
              </q-input>
            </div>
          </div>

          <!-- Role and Status -->
          <div class="row q-gutter-md">
            <div class="col-12 col-sm-6">
              <q-select
                v-model="form.roles"
                :options="roleOptions"
                label="Roles"
                outlined
                multiple
                use-chips
              />
            </div>
            <div class="col-12 col-sm-6">
              <q-toggle
                v-model="form.is_active"
                label="Active User"
                color="positive"
              />
            </div>
          </div>

          <!-- Permissions -->
          <div class="permissions-section">
            <div class="text-subtitle2 q-mb-sm">Permissions</div>
            <div class="row q-gutter-sm">
              <div
                v-for="permission in availablePermissions"
                :key="permission.value"
                class="col-12 col-sm-6 col-md-4"
              >
                <q-checkbox
                  v-model="form.permissions"
                  :val="permission.value"
                  :label="permission.label"
                  color="primary"
                />
              </div>
            </div>
          </div>
        </q-form>
      </q-card-section>

      <q-separator />

      <q-card-actions align="right" class="dialog-actions">
        <q-btn flat label="Cancel" @click="closeDialog" />
        <q-btn
          color="primary"
          label="Create User"
          @click="createUser"
          :loading="form.processing"
          :disable="!isFormValid"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useQuasar } from 'quasar'
import { useForm } from '@inertiajs/vue3'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'created'])

const $q = useQuasar()

// Reactive data
const showDialog = ref(false)
const showPassword = ref(false)
const showPasswordConfirm = ref(false)

const form = useForm({
  name: '',
  email: '',
  password: '',
  password_confirmation: '',
  roles: [],
  permissions: [],
  is_active: true,
})

// Options
const roleOptions = [
  { label: 'Super Admin', value: 'super_admin' },
  { label: 'Admin', value: 'admin' },
  { label: 'Manager', value: 'manager' },
  { label: 'Editor', value: 'editor' },
  { label: 'Viewer', value: 'viewer' }
]

const availablePermissions = [
  { label: 'Manage Users', value: 'manage_users' },
  { label: 'Manage Currencies', value: 'manage_currencies' },
  { label: 'Manage Exchange Rates', value: 'manage_rates' },
  { label: 'View Analytics', value: 'view_analytics' },
  { label: 'System Settings', value: 'system_settings' },
  { label: 'Export Data', value: 'export_data' }
]

// Computed properties
const isFormValid = computed(() => {
  return form.name &&
         form.email &&
         form.password &&
         form.password_confirmation &&
         form.password === form.password_confirmation
})

// Methods
const handleAvatarUpload = (event) => {
  const file = event.target.files[0]
  if (file) {
    form.value.avatar = file

    // Create preview
    const reader = new FileReader()
    reader.onload = (e) => {
      form.value.avatar_preview = e.target.result
    }
    reader.readAsDataURL(file)
  }
}

const createUser = () => {
  if (!isFormValid.value) return

  form.post(route('admin.users.store'), {
    onSuccess: () => {
      $q.notify({
        type: 'positive',
        message: 'User created successfully',
        position: 'top'
      })
      emit('created')
      closeDialog()
      resetForm()
    },
    onError: (errors) => {
      // Handle validation errors
      Object.keys(errors).forEach(field => {
        $q.notify({
          type: 'negative',
          message: errors[field],
          position: 'top'
        })
      })
    }
  })
}

const closeDialog = () => {
  emit('update:modelValue', false)
}

const resetForm = () => {
  form.reset()
  showPassword.value = false
  showPasswordConfirm.value = false
}

// Watchers
watch(() => props.modelValue, (newVal) => {
  showDialog.value = newVal
  if (newVal) {
    resetForm()
  }
})

watch(showDialog, (newVal) => {
  if (!newVal) {
    emit('update:modelValue', false)
  }
})
</script>

<style scoped>
.create-user-dialog {
  width: 100%;
  max-width: 600px;
  border-radius: 12px;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px 16px;
}

.dialog-content {
  padding: 24px;
  max-height: 70vh;
  overflow-y: auto;
}

.dialog-actions {
  padding: 16px 24px 20px;
}

.avatar-section {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
}

.avatar-preview {
  border: 3px solid #e5e7eb;
  background: white;
}

.permissions-section {
  margin-top: 24px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
}

/* Dark mode */
.body--dark .avatar-section,
.body--dark .permissions-section {
  background: #374151;
}

.body--dark .avatar-preview {
  border-color: #6b7280;
  background: #4b5563;
}

/* Responsive */
@media (max-width: 600px) {
  .create-user-dialog {
    margin: 16px;
    max-width: calc(100vw - 32px);
  }

  .dialog-content {
    padding: 16px;
  }

  .avatar-section {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }
}
</style>
