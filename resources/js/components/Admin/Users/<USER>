<template>
  <q-dialog v-model="showDialog" persistent>
    <q-card class="edit-user-dialog">
      <q-card-section class="dialog-header">
        <div class="text-h6">Edit User</div>
        <q-btn flat round dense icon="close" @click="closeDialog" />
      </q-card-section>

      <q-separator />

      <q-card-section class="dialog-content">
        <q-form @submit="updateUser" class="q-gutter-md">
          <!-- Avatar Upload -->
          <div class="avatar-section">
            <q-avatar size="80px" class="avatar-preview">
              <img v-if="form.avatar_preview || user?.avatar" :src="form.avatar_preview || user?.avatar" />
              <img v-else src="/images/default-avatar.svg" />
            </q-avatar>
            <q-btn
              flat
              icon="camera_alt"
              label="Change Avatar"
              @click="$refs.avatarInput.click()"
              class="q-ml-md"
            />
            <input
              ref="avatarInput"
              type="file"
              accept="image/*"
              @change="handleAvatarUpload"
              style="display: none"
            />
          </div>

          <!-- Basic Information -->
          <div class="row q-gutter-md">
            <div class="col-12 col-sm-6">
              <q-input
                v-model="form.name"
                label="Full Name *"
                outlined
                :rules="[val => !!val || 'Name is required']"
              />
            </div>
            <div class="col-12 col-sm-6">
              <q-input
                v-model="form.email"
                label="Email Address *"
                type="email"
                outlined
                :rules="[
                  val => !!val || 'Email is required',
                  val => /.+@.+\..+/.test(val) || 'Email must be valid'
                ]"
              />
            </div>
          </div>

          <!-- Password (Optional for edit) -->
          <div class="password-section">
            <q-expansion-item
              icon="lock"
              label="Change Password"
              header-class="text-primary"
            >
              <div class="row q-gutter-md q-pa-md">
                <div class="col-12 col-sm-6">
                  <q-input
                    v-model="form.password"
                    label="New Password"
                    :type="showPassword ? 'text' : 'password'"
                    outlined
                    :rules="[
                      val => !val || val.length >= 8 || 'Password must be at least 8 characters'
                    ]"
                  >
                    <template v-slot:append>
                      <q-icon
                        :name="showPassword ? 'visibility_off' : 'visibility'"
                        class="cursor-pointer"
                        @click="showPassword = !showPassword"
                      />
                    </template>
                  </q-input>
                </div>
                <div class="col-12 col-sm-6">
                  <q-input
                    v-model="form.password_confirmation"
                    label="Confirm New Password"
                    :type="showPasswordConfirm ? 'text' : 'password'"
                    outlined
                    :rules="[
                      val => !form.password || !!val || 'Password confirmation is required',
                      val => !form.password || val === form.password || 'Passwords do not match'
                    ]"
                  >
                    <template v-slot:append>
                      <q-icon
                        :name="showPasswordConfirm ? 'visibility_off' : 'visibility'"
                        class="cursor-pointer"
                        @click="showPasswordConfirm = !showPasswordConfirm"
                      />
                    </template>
                  </q-input>
                </div>
              </div>
            </q-expansion-item>
          </div>

          <!-- Role and Status -->
          <div class="row q-gutter-md">
            <div class="col-12 col-sm-6">
              <q-select
                v-model="form.role"
                :options="roleOptions"
                label="Role *"
                outlined
                :rules="[val => !!val || 'Role is required']"
              />
            </div>
            <div class="col-12 col-sm-6">
              <q-toggle
                v-model="form.is_active"
                label="Active User"
                color="positive"
              />
            </div>
          </div>

          <!-- Permissions -->
          <div class="permissions-section">
            <div class="text-subtitle2 q-mb-sm">Permissions</div>
            <div class="row q-gutter-sm">
              <div
                v-for="permission in availablePermissions"
                :key="permission.value"
                class="col-12 col-sm-6 col-md-4"
              >
                <q-checkbox
                  v-model="form.permissions"
                  :val="permission.value"
                  :label="permission.label"
                  color="primary"
                />
              </div>
            </div>
          </div>

          <!-- User Statistics -->
          <div class="stats-section">
            <div class="text-subtitle2 q-mb-sm">User Statistics</div>
            <div class="row q-gutter-md">
              <div class="col-6 col-sm-3">
                <q-card flat bordered class="stat-card">
                  <q-card-section class="text-center">
                    <div class="text-h6">{{ user?.login_count || 0 }}</div>
                    <div class="text-caption">Total Logins</div>
                  </q-card-section>
                </q-card>
              </div>
              <div class="col-6 col-sm-3">
                <q-card flat bordered class="stat-card">
                  <q-card-section class="text-center">
                    <div class="text-h6">{{ user?.actions_count || 0 }}</div>
                    <div class="text-caption">Actions</div>
                  </q-card-section>
                </q-card>
              </div>
              <div class="col-6 col-sm-3">
                <q-card flat bordered class="stat-card">
                  <q-card-section class="text-center">
                    <div class="text-h6">{{ formatDate(user?.created_at) }}</div>
                    <div class="text-caption">Member Since</div>
                  </q-card-section>
                </q-card>
              </div>
              <div class="col-6 col-sm-3">
                <q-card flat bordered class="stat-card">
                  <q-card-section class="text-center">
                    <div class="text-h6">{{ formatDate(user?.last_login) }}</div>
                    <div class="text-caption">Last Login</div>
                  </q-card-section>
                </q-card>
              </div>
            </div>
          </div>
        </q-form>
      </q-card-section>

      <q-separator />

      <q-card-actions align="right" class="dialog-actions">
        <q-btn flat label="Cancel" @click="closeDialog" />
        <q-btn
          color="primary"
          label="Update User"
          @click="updateUser"
          :loading="loading"
          :disable="!isFormValid"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useQuasar } from 'quasar'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  user: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:modelValue', 'updated'])

const $q = useQuasar()

// Reactive data
const showDialog = ref(false)
const loading = ref(false)
const showPassword = ref(false)
const showPasswordConfirm = ref(false)

const form = ref({
  name: '',
  email: '',
  password: '',
  password_confirmation: '',
  role: null,
  is_active: true,
  permissions: [],
  avatar: null,
  avatar_preview: null
})

// Options
const roleOptions = [
  { label: 'Super Admin', value: 'super_admin' },
  { label: 'Admin', value: 'admin' },
  { label: 'Manager', value: 'manager' },
  { label: 'Editor', value: 'editor' },
  { label: 'Viewer', value: 'viewer' }
]

const availablePermissions = [
  { label: 'Manage Users', value: 'manage_users' },
  { label: 'Manage Currencies', value: 'manage_currencies' },
  { label: 'Manage Exchange Rates', value: 'manage_rates' },
  { label: 'View Analytics', value: 'view_analytics' },
  { label: 'System Settings', value: 'system_settings' },
  { label: 'Export Data', value: 'export_data' }
]

// Computed properties
const isFormValid = computed(() => {
  const passwordValid = !form.value.password ||
    (form.value.password === form.value.password_confirmation && form.value.password.length >= 8)

  return form.value.name &&
         form.value.email &&
         form.value.role &&
         passwordValid
})

// Methods
const handleAvatarUpload = (event) => {
  const file = event.target.files[0]
  if (file) {
    form.value.avatar = file

    // Create preview
    const reader = new FileReader()
    reader.onload = (e) => {
      form.value.avatar_preview = e.target.result
    }
    reader.readAsDataURL(file)
  }
}

const updateUser = async () => {
  if (!isFormValid.value) return

  loading.value = true

  try {
    // Import API utility
    const { userApi } = await import('@/utils/api.js')

    // Prepare data for API
    const userData = {
      name: form.value.name,
      email: form.value.email,
      is_active: form.value.is_active,
      roles: form.value.role ? [form.value.role] : [],
      permissions: form.value.permissions || []
    }

    // Only include password if it's provided
    if (form.value.password) {
      userData.password = form.value.password
      userData.password_confirmation = form.value.password_confirmation
    }

    const data = await userApi.updateUser(props.user.id, userData)

    $q.notify({
      type: 'positive',
      message: data.message || 'User updated successfully',
      position: 'top'
    })

    emit('updated', data.data)
    closeDialog()

  } catch (error) {
    const { handleApiError } = await import('@/utils/api.js')
    handleApiError(error, $q)
  } finally {
    loading.value = false
  }
}

const closeDialog = () => {
  emit('update:modelValue', false)
}

const populateForm = () => {
  if (props.user) {
    form.value = {
      name: props.user.name || '',
      email: props.user.email || '',
      password: '',
      password_confirmation: '',
      role: props.user.role || null,
      is_active: props.user.is_active ?? true,
      permissions: props.user.permissions || [],
      avatar: null,
      avatar_preview: null
    }
  }
}

const formatDate = (date) => {
  if (!date) return 'N/A'
  return new Date(date).toLocaleDateString()
}

// Watchers
watch(() => props.modelValue, (newVal) => {
  showDialog.value = newVal
  if (newVal && props.user) {
    populateForm()
  }
})

watch(showDialog, (newVal) => {
  if (!newVal) {
    emit('update:modelValue', false)
  }
})
</script>

<style scoped>
.edit-user-dialog {
  width: 100%;
  max-width: 700px;
  border-radius: 12px;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px 16px;
}

.dialog-content {
  padding: 24px;
  max-height: 70vh;
  overflow-y: auto;
}

.dialog-actions {
  padding: 16px 24px 20px;
}

.avatar-section {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
}

.avatar-preview {
  border: 3px solid #e5e7eb;
  background: white;
}

.password-section {
  margin: 24px 0;
}

.permissions-section,
.stats-section {
  margin-top: 24px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
}

.stat-card {
  border-radius: 8px;
}

/* Dark mode */
.body--dark .avatar-section,
.body--dark .permissions-section,
.body--dark .stats-section {
  background: #374151;
}

.body--dark .avatar-preview {
  border-color: #6b7280;
  background: #4b5563;
}

/* Responsive */
@media (max-width: 700px) {
  .edit-user-dialog {
    margin: 16px;
    max-width: calc(100vw - 32px);
  }

  .dialog-content {
    padding: 16px;
  }

  .avatar-section {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }
}
</style>
